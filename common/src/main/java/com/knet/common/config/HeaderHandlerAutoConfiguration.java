package com.knet.common.config;

import com.knet.common.aspect.ModifyHeaderAspect;
import com.knet.common.handler.HeaderHandler;
import com.knet.common.handler.impl.ApiKeyHeaderHandler;
import com.knet.common.handler.impl.UserTokenHeaderHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/19
 * @description: Header处理器自动配置类，只在Servlet环境中生效
 */
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class HeaderHandlerAutoConfiguration {

    @Bean
    public UserTokenHeaderHandler userTokenHeaderHandler() {
        return new UserTokenHeaderHandler();
    }

    @Bean
    public ApiKeyHeaderHandler apiKeyHeaderHandler() {
        return new ApiKeyHeaderHandler();
    }

    @Bean
    public ModifyHeaderAspect modifyHeaderAspect(List<HeaderHandler> handlers) {
        return new ModifyHeaderAspect(handlers);
    }
}
