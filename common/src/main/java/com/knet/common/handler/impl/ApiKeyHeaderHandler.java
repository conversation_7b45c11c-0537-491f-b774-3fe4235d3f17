package com.knet.common.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.context.ApiKeyContext;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.common.handler.HeaderHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

import static com.knet.common.constants.SystemConstant.TOKEN;
import static com.knet.common.constants.SystemConstant.X_API_KEY;

/**
 * <AUTHOR>
 * @date 2025/12/19
 * @description: API Key请求头处理器
 */
@Slf4j
@Component
public class ApiKeyHeaderHandler implements HeaderHandler {

    @Value("${account.kg-access-token:}")
    private String kgAccessToken;

    @Value("${account.kg-account:}")
    private String kgAccount;

    @Override
    public void handleHeader(HttpServletRequest request, ModifyHeader modifyHeader, String headerName, String headerValue) {
        // 验证必填性
        if (modifyHeader.required() && StrUtil.isEmpty(headerValue)) {
            log.error("Required header [{}] is missing in request", headerName);
            throw new ServiceException("缺少必要的请求头: " + headerName);
        }

        // 处理X-API-KEY请求头
        if (X_API_KEY.equals(headerName)) {
            if (StrUtil.isNotEmpty(kgAccessToken) && !StrUtil.equals(kgAccessToken, headerValue)) {
                log.error("请求头 [{}] 的值 [{}] accessToken不正确", headerName, headerValue);
                throw new ServiceException("accessToken不正确");
            }
            if (StrUtil.isNotEmpty(kgAccount)) {
                ApiKeyContext.setApiKey(kgAccount);
            }
            log.debug("Set API key [{}] from header [{}]", StrUtil.isEmpty(headerValue) ? "EMPTY" : "******", headerName);
        }

        // 处理TOKEN请求头
        if (TOKEN.equals(headerName) && StrUtil.isNotEmpty(headerValue)) {
            UserContext.setContext(headerValue);
            log.debug("Set user token from header [{}]", headerName);
        }
    }

    @Override
    public void clearContext() {
        ApiKeyContext.clear();
        UserContext.clear();
        log.debug("API key and user context cleared");
    }

    @Override
    public String getHandlerType() {
        return "API_KEY";
    }
}
