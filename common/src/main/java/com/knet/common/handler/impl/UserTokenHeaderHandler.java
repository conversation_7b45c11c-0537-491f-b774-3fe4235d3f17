package com.knet.common.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.common.handler.HeaderHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

import static com.knet.common.constants.SystemConstant.TOKEN;

/**
 * <AUTHOR>
 * @date 2025/12/19
 * @description: 用户Token请求头处理器
 */
@Slf4j
@Component
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class UserTokenHeaderHandler implements HeaderHandler {

    @Override
    public void handleHeader(HttpServletRequest request, ModifyHeader modifyHeader, String headerName, String headerValue) {
        // 验证必填性
        if (modifyHeader.required() && StrUtil.isEmpty(headerValue)) {
            log.error("Required header [{}] is missing in request", headerName);
            throw new ServiceException("缺少必要的请求头: " + headerName);
        }

        // 处理TOKEN请求头
        if (TOKEN.equals(headerName) && StrUtil.isNotEmpty(headerValue)) {
            UserContext.setContext(headerValue);
            log.debug("Set user token from header [{}]", headerName);
        }
    }

    @Override
    public void clearContext() {
        UserContext.clear();
        log.debug("User context cleared");
    }

    @Override
    public String getHandlerType() {
        return "USER_TOKEN";
    }
}
