# ModifyHeader 注解重构说明

## 概述

为了解决各个模块中 `ModifyHeaderAspect` 切面和 `UserContext`、`ApiKeyContext` 重复实现的问题，我们将这些通用功能提取到了 `common` 模块中，采用策略模式来处理不同类型的请求头。

## 重构内容

### 1. 通用上下文类
- `com.knet.common.context.UserContext` - 通用用户上下文
- `com.knet.common.context.ApiKeyContext` - 通用API Key上下文

### 2. 请求头处理器
- `com.knet.common.handler.HeaderHandler` - 请求头处理器接口
- `com.knet.common.handler.impl.UserTokenHeaderHandler` - 用户Token处理器
- `com.knet.common.handler.impl.ApiKeyHeaderHandler` - API Key处理器

### 3. 通用切面
- `com.knet.common.aspect.ModifyHeaderAspect` - 通用ModifyHeader注解切面

### 4. 增强的注解
- `com.knet.common.annotation.ModifyHeader` - 增加了 `handlerType` 参数

## 使用方式

### 1. 用户Token验证
```java
@ModifyHeader(value = "token", handlerType = "USER_TOKEN")
@PostMapping("/user/info")
public HttpResult<UserInfo> getUserInfo() {
    String token = UserContext.getContext();
    // 业务逻辑
}
```

### 2. API Key验证
```java
@ModifyHeader(handlerType = "API_KEY")  // 默认使用 x-api-key
@PostMapping("/api/products")
public HttpResult<List<Product>> getProducts() {
    String apiKey = ApiKeyContext.getApiKey();
    // 业务逻辑
}
```

### 3. 自定义请求头
```java
@ModifyHeader(value = "custom-header", handlerType = "USER_TOKEN")
@PostMapping("/custom")
public HttpResult<String> customEndpoint() {
    // 业务逻辑
}
```

## 配置说明

### API Key处理器配置
API Key处理器需要在配置文件中设置以下参数：

```yaml
account:
  kg-access-token: your-access-token
  kg-account: your-account
```

## 处理器类型

- `USER_TOKEN`: 处理用户Token，将token存储到UserContext中
- `API_KEY`: 处理API Key验证，同时支持Token和API Key
- `CUSTOM`: 预留的自定义处理器类型

## 迁移指南

### 1. 删除重复文件
已删除以下重复实现：
- `goods-services/src/main/java/com/knet/goods/system/interceptor/ModifyHeaderAspect.java`
- `payment-services/src/main/java/com/knet/payment/system/interceptor/ModifyHeaderAspect.java`
- `user-services/src/main/java/com/knet/user/system/interceptor/ModifyHeaderAspect.java`
- 各模块中的 `UserContext.java` 和 `ApiKeyContext.java`

### 2. 更新import语句
将以下import语句：
```java
import com.knet.xxx.system.config.UserContext;
import com.knet.xxx.system.config.ApiKeyContext;
```

更新为：
```java
import com.knet.common.context.UserContext;
import com.knet.common.context.ApiKeyContext;
```

### 3. 更新注解使用
将：
```java
@ModifyHeader("token")
@ModifyHeader
```

更新为：
```java
@ModifyHeader(value = "token", handlerType = "USER_TOKEN")
@ModifyHeader(handlerType = "API_KEY")
```

## 优势

1. **代码复用**: 消除了重复的切面和上下文实现
2. **统一管理**: 所有请求头处理逻辑集中在common模块
3. **易于扩展**: 通过策略模式可以轻松添加新的处理器类型
4. **类型安全**: 通过handlerType参数明确指定处理器类型
5. **向后兼容**: 保持了原有的API接口不变

## 注意事项

1. 确保各模块的启动类包含了common模块的包扫描：
   ```java
   @ComponentScan(basePackages = {"com.knet.xxx", "com.knet.common"})
   ```

2. API Key处理器需要正确配置access token和account信息

3. 所有使用@ModifyHeader注解的地方都需要明确指定handlerType参数
