package com.knet.payment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.context.UserContext;
import com.knet.common.enums.WalletRecordType;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RandomStrUtil;
import com.knet.payment.mapper.SysWalletRecordMapper;
import com.knet.payment.model.dto.req.UserWalletQueryRequest;
import com.knet.payment.model.dto.res.SysWalletRecordResp;
import com.knet.payment.model.entity.SysWalletRecord;
import com.knet.payment.service.ISysWalletRecordService;
import com.knet.payment.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【sys_wallet_record(用户钱包记录表)】的数据库操作Service实现
 * @date 2025-03-12 15:17:41
 */
@Slf4j
@Service
public class SysWalletRecordServiceImpl extends ServiceImpl<SysWalletRecordMapper, SysWalletRecord> implements ISysWalletRecordService {

    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private RandomStrUtil randomStrUtil;

    /**
     * 查询用户钱包记录
     *
     * @param request 用户ID
     * @return 用户钱包记录列表
     */
    @Override
    public IPage<SysWalletRecordResp> findWalletRecordList(UserWalletQueryRequest request) {
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        if (StrUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        LambdaQueryWrapper<SysWalletRecord> queryWrapper = new LambdaQueryWrapper<>();
        request.setUserId(Long.valueOf(userId));
        queryWrapper
                .eq(SysWalletRecord::getUserId, request.getUserId())
                .eq(BeanUtil.isNotEmpty(request.getWalletRecordType()), SysWalletRecord::getType, request.getWalletRecordType())
                .orderByDesc(SysWalletRecord::getRecordId);
        Page<SysWalletRecord> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SysWalletRecord> userAddressPage = baseMapper.selectPage(page, queryWrapper);
        return userAddressPage.convert(SysWalletRecord::mapToSysWalletRecordResp);
    }

    /**
     * 创建钱包记录
     *
     * @param userId    用户ID
     * @param amount    变动金额
     * @param type      记录类型
     * @param paymentId 支付流水ID（可选）
     * @param orderId   订单ID（可选）
     * @return 记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String createWalletRecord(Long userId, BigDecimal amount, WalletRecordType type, String paymentId, String orderId, String remarks) {
        log.info("创建钱包记录: userId={}, amount={}, type={}, paymentId={}, orderId={},remarks={}", userId, amount, type, paymentId, orderId, remarks);
        String recordId = randomStrUtil.getWalletRecordId();
        SysWalletRecord walletRecord = SysWalletRecord
                .builder()
                .recordId(recordId)
                .userId(userId)
                .amount(amount)
                .type(type)
                .paymentId(paymentId)
                .orderId(orderId)
                .remarks(remarks)
                .build();
        boolean saved = this.save(walletRecord);
        if (!saved) {
            log.error("钱包记录保存失败: recordId={}", recordId);
            throw new ServiceException("钱包记录保存失败");
        }
        log.info("钱包记录创建成功: recordId={}", recordId);
        return recordId;
    }
}




